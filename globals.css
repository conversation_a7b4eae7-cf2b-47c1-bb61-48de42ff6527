*,
*::before,
*::after {
	box-sizing: border-box;
}

ul[class],
ol[class] {
	padding: 0;
}

body,
h1,
h2,
h3,
h4,
p,
ul[class],
ol[class],
li,
figure,
figcaption,
blockquote,
dl,
dd {
	margin: 0;
	color: #333333;
}

body {
	min-height: 100vh;
	scroll-behavior: smooth;
	background-color: #1a1a1a;
	margin-left: auto;
	margin-right: auto;
	text-align: center;
	font-family: "Montserrat", sans-serif;
}

ul[class],
ol[class] {
	list-style: none;
}

a:not([class]) {
	text-decoration-skip-ink: auto;
}

/* Упрощаем работу с изображениями */
img {
	max-width: 100%;
	display: block;
}

article > * + * {
	margin-top: 1em;
}

input,
button,
textarea,
select {
	font: inherit;
}

.flex {
	display: flex;
}
